import type { FC } from 'react'
import React from 'react'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { WinnerCard } from '@modules/winners/components'
import type { DynamicallyRenderedWinnersConfigType } from '@modules/winners/WinnersWidget.schema'
import styles from '@modules/winners/WinnersWidget.module.scss'

export interface IWinnersWidgetProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedWinnersConfigType
}

// TODO: Replace with API request once ready
const winners = [
  {
    username: 'johnwa',
    amount: '109000',
    currency: 'GC',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
  },
  {
    username: 'johnwa',
    amount: '109000',
    currency: 'GC',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
  },
  {
    username: 'johnwa',
    amount: '109000',
    currency: 'GC',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
  },
  {
    username: 'johnwa',
    amount: '109000',
    currency: 'GC',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
  },
  {
    username: 'johnwa',
    amount: '109000',
    currency: 'GC',
    thumbnail: 'https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/winlabs/images/game-thumb/gorilla.png',
  },
]

const WinnersWidget: FC<IWinnersWidgetProps> = ({ config }) => {
  if (!config) {
    return null
  }

  const { title } = config

  return (
    <div className={styles.container}>
      {!!title && <h2 className={styles.title}>{title}</h2>}
      <div className={styles.scrollContainer}>
        {winners.map((winner, index) => (
          <div key={`${winner.username}-${winner.gameName}-${index}`}>
            <WinnerCard {...winner} />
          </div>
        ))}
      </div>
    </div>
  )
}

export default WinnersWidget
