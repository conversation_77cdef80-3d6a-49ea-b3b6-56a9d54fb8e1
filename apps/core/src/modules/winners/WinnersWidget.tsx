import type { FC } from 'react'
import React from 'react'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { WinnerCard } from '@modules/winners/components'
import type { DynamicallyRenderedWinnersConfigType } from '@modules/winners/WinnersWidget.schema'
import styles from '@modules/winners/WinnersWidget.module.scss'

export interface IWinnersWidgetProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedWinnersConfigType
}

// TODO: Replace with API request once ready
const winners = [
  {
    username: 'johnwa...',
    winAmount: '109.5K',
    gameName: 'GORILLA MAYHEM',
    gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/gorilla-mayhem.png',
  },
  {
    username: 'johnwa...',
    winAmount: '109.5K', 
    gameName: 'MILKY WAYS',
    gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/milky-ways.png',
  },
  {
    username: 'johnwa...',
    winAmount: '109.5K',
    gameName: 'VAMPIRES VS WOLVES',
    gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/vampires-vs-wolves.png',
  },
  {
    username: 'johnwa...',
    winAmount: '109.5K',
    gameName: 'GORILLA MAYHEM',
    gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/gorilla-mayhem.png',
  },
  {
    username: 'johnwa...',
    winAmount: '109.5K',
    gameName: 'WINTER WONDERS',
    gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/winter-wonders.png',
  },
  {
    username: 'johnwa...',
    winAmount: '109.5K',
    gameName: 'GODS OF GIZA',
    gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/gods-of-giza.png',
  },
  {
    username: 'johnwa...',
    winAmount: '109.5K',
    gameName: 'FORTUNE TIGER',
    gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/fortune-tiger.png',
  },
  {
    username: 'johnwa...',
    winAmount: '109.5K',
    gameName: 'MEGA MOOLAH',
    gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/mega-moolah.png',
  },
  {
    username: 'johnwa...',
    winAmount: '109.5K',
    gameName: 'GOLDEN JOKER',
    gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/golden-joker.png',
  },
  {
    username: 'johnwa...',
    winAmount: '109.5K',
    gameName: 'MEGA MOOLAH',
    gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/mega-moolah.png',
  },
  {
    username: 'johnwa...',
    winAmount: '109.5K',
    gameName: 'THE GREAT GORILLA',
    gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/great-gorilla.png',
  },
  {
    username: 'johnwa...',
    winAmount: '109.5K',
    gameName: 'GORILLA MAYHEM',
    gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/gorilla-mayhem.png',
  },
  {
    username: 'johnwa...',
    winAmount: '109.5K',
    gameName: 'MILKY WAYS',
    gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/milky-ways.png',
  },
  {
    username: 'johnwa...',
    winAmount: '109.5K',
    gameName: 'VAMPIRES VS WOLVES',
    gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/vampires-vs-wolves.png',
  },
]

const WinnersWidget: FC<IWinnersWidgetProps> = ({ config }) => {
  if (!config) {
    return null
  }

  const { title } = config

  return (
    <div className={styles.container}>
      {!!title && <h2 className={styles.title}>{title}</h2>}
      <div className={styles.scrollContainer}>
        {winners.map((winner, index) => (
          <div key={`${winner.username}-${winner.gameName}-${index}`}>
            <WinnerCard {...winner} />
          </div>
        ))}
      </div>
    </div>
  )
}

export default WinnersWidget
