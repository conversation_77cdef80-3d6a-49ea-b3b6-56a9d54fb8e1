import { z } from 'zod'
import { DynamicallyRenderedContentBaseConfigSchema } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { DynamicallyRenderedWidget } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'

export const DynamicallyRenderedWinnersConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedWidget.WINNERS),
  meta: z.object({
    title: z.string().optional(),
  }),
})

export type DynamicallyRenderedWinnersConfigType = z.infer<typeof DynamicallyRenderedWinnersConfigSchema>['meta']
