import Image from 'next/image'
import { Card } from '@components/Card'
import styles from '@modules/winners/components/WinnerCard.module.scss'

export interface IWinnerCardProps {
  username: string
  winAmount: string
  gameName: string
  gameImageSrc: string
  currency?: string
}

const WinnerCard = ({
  username,
  winAmount,
  gameName,
  gameImageSrc,
  currency = '$',
}: IWinnerCardProps) => {
  return (
    <Card className={styles.winnerCard} aria-label="Winner Card">
      <div className={styles.gameImage}>
        <Image 
          src={gameImageSrc} 
          alt={`${gameName} game thumbnail`} 
          width={48} 
          height={48}
          className={styles.thumbnail}
        />
      </div>
      
      <div className={styles.content}>
        <div className={styles.userInfo}>
          <p className={styles.username}>{username}</p>
          <p className={styles.winAmount}>
            {currency}{winAmount}
          </p>
        </div>
        <p className={styles.gameName}>{gameName}</p>
      </div>
    </Card>
  )
}

export default WinnerCard
