import type { <PERSON>a, StoryObj } from '@storybook/react'
import { WinnerCard } from '@modules/winners/components'

const meta: Meta<typeof WinnerCard> = {
  title: 'Modules/Winners/WinnerCard',
  component: WinnerCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    username: {
      control: 'text',
      description: 'The username of the winner',
    },
    winAmount: {
      control: 'text',
      description: 'The amount won by the user',
    },
    gameName: {
      control: 'text',
      description: 'The name of the game',
    },
    gameImageSrc: {
      control: 'text',
      description: 'The source URL for the game thumbnail image',
    },
    currency: {
      control: 'text',
      description: 'The currency symbol',
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    username: 'johnwa...',
    winAmount: '109.5K',
    gameName: 'GORILLA MAYHEM',
    gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/gorilla-mayhem.png',
    currency: '$',
  },
}

export const LongGameName: Story = {
  args: {
    username: 'player123...',
    winAmount: '250.8K',
    gameName: 'VAMPIRES VS WOLVES ULTIMATE EDITION',
    gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/vampires-vs-wolves.png',
    currency: '$',
  },
}

export const SmallWin: Story = {
  args: {
    username: 'winner...',
    winAmount: '1.2K',
    gameName: 'FORTUNE TIGER',
    gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/fortune-tiger.png',
    currency: '$',
  },
}

export const EurosCurrency: Story = {
  args: {
    username: 'europa...',
    winAmount: '89.3K',
    gameName: 'MEGA MOOLAH',
    gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/mega-moolah.png',
    currency: '€',
  },
}

// Showcase multiple winner cards in a horizontal layout
export const WinnersShowcase: Story = {
  render: () => {
    const winners = [
      {
        username: 'johnwa...',
        winAmount: '109.5K',
        gameName: 'GORILLA MAYHEM',
        gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/gorilla-mayhem.png',
      },
      {
        username: 'player...',
        winAmount: '89.2K',
        gameName: 'MILKY WAYS',
        gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/milky-ways.png',
      },
      {
        username: 'winner...',
        winAmount: '156.7K',
        gameName: 'VAMPIRES VS WOLVES',
        gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/vampires-vs-wolves.png',
      },
      {
        username: 'lucky...',
        winAmount: '203.4K',
        gameName: 'WINTER WONDERS',
        gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/winter-wonders.png',
      },
      {
        username: 'gamer...',
        winAmount: '78.9K',
        gameName: 'GODS OF GIZA',
        gameImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/gods-of-giza.png',
      },
    ]

    return (
      <div style={{ 
        display: 'flex', 
        gap: '8px', 
        overflowX: 'auto', 
        padding: '20px',
        maxWidth: '800px'
      }}>
        {winners.map((winner, index) => (
          <div key={index} style={{ flexShrink: 0 }}>
            <WinnerCard {...winner} />
          </div>
        ))}
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: 'Multiple winner cards displayed in a horizontal scrolling layout, similar to how they appear in the winners widget.',
      },
    },
  },
}
