@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;
@use '@theme/mixins.scss' as *;

.winnerCard {
  width: calculate-rem(120px);
  height: calculate-rem(80px);
  position: relative;
  border-radius: calculate-rem(8px);
  display: flex;
  align-items: center;
  gap: calculate-rem(8px);
  padding: calculate-rem(8px);
  background: $color-surface-100;
  border: 1px solid $color-surface-300;
  flex-shrink: 0;

  @media (max-width: $breakpoint-mobile) {
    width: calculate-rem(110px);
    height: calculate-rem(72px);
    gap: calculate-rem(6px);
    padding: calculate-rem(6px);
  }
}

.gameImage {
  flex-shrink: 0;
  width: calculate-rem(48px);
  height: calculate-rem(48px);
  border-radius: calculate-rem(6px);
  overflow: hidden;
  background: $color-surface-200;

  @media (max-width: $breakpoint-mobile) {
    width: calculate-rem(40px);
    height: calculate-rem(40px);
  }
}

.thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: inherit;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: calculate-rem(2px);
  min-width: 0; // Allow text truncation
}

.userInfo {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(1px);
}

.username {
  font-size: calculate-rem(12px);
  font-weight: 600;
  color: $color-on-secondary;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  @media (max-width: $breakpoint-mobile) {
    font-size: calculate-rem(11px);
  }
}

.winAmount {
  font-size: calculate-rem(11px);
  font-weight: 700;
  color: $color-primary;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  @media (max-width: $breakpoint-mobile) {
    font-size: calculate-rem(10px);
  }
}

.gameName {
  font-size: calculate-rem(10px);
  font-weight: 400;
  color: $color-surface-600;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;

  @media (max-width: $breakpoint-mobile) {
    font-size: calculate-rem(9px);
  }
}
